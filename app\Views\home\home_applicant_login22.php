<?= $this->extend('templates/home_template') ?>

<?= $this->section('content') ?>


<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Applicant Login</h3>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('swal_icon')): ?>
                        <div class="alert alert-<?= session()->getFlashdata('swal_icon') == 'success' ? 'success' : (session()->getFlashdata('swal_icon') == 'error' ? 'danger' : 'warning') ?>">
                            <strong><?= session()->getFlashdata('swal_title') ?></strong><br>
                            <?= session()->getFlashdata('swal_text') ?>
                        </div>
                    <?php endif; ?>

                    <?= form_open('applicant/login') ?>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-red">Login</button>
                    </div>

                    <?= form_close() ?>

                    <div class="text-center mt-3">
                        <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">Forgot Password?</a>
                    </div>

                    <div class="text-center mt-3">
                        <p>Don't have an account? <a href="<?= base_url('applicant/register') ?>" class="text-decoration-none">Register here</a></p>
                        <a href="<?= base_url('/') ?>" class="text-decoration-none">Back to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Forgot Password Modal -->
<div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="forgotPasswordModalLabel">Forgot Password</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="<?= base_url('applicant/forgot-password') ?>" method="post">
        <?= csrf_field() ?>
        <div class="modal-body">
          <div class="mb-3">
            <label for="forgot_email" class="form-label">Enter your registered email address</label>
            <input type="email" class="form-control" id="forgot_email" name="email" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Send Reset Code</button>
        </div>
      </form>
    </div>
  </div>
</div>




<?= $this->endSection() ?>
